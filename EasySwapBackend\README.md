## EasySwap Web Server Backend project

## 如何运行项目

### 准备条件

1. 确保将下面三个 repo 都pull同一个本地目录
    - https://github.com/ProjectsTask/EasySwapBackend
    - https://github.com/ProjectsTask/EasySwapBase
    - https://github.com/ProjectsTask/EasySwapSync
    -- EasySwapBackend
    -- EasySwapBase
    -- EasySwapSync

2. 复制下配置文件 `cp config/config.toml.example  config/config.toml`

3. 打开 go.mod 这一行的注释，然后 终端执行 `go mod tidy`
```shell
replace github.com/ProjectsTask/EasySwapBase => ../EasySwapBase
```

3. infura 上面注册一个账号，给 `chain_supported.endpoint` 替换掉

4. 通过部署 https://github.com/ProjectsTask/EasySwapContract（看里面的README.md）得到订单簿合约的地址 替换掉 config/config.toml 中的 easyswap_market.contract 

5. docker 上面 运行一下 `EasySwapSync/docker-compose.yml` 给 redis 和 mysql 环境整好

6. 运行以下指令，如果你是 goland ide 你可以直接到 src/main.go 上面点击启动
   
```shell
cd src
go run main.go
```
如图：恭喜你后端 的api 服务运行成功了！
![img.png](img.png)











## **系统架构总结**

- main.go中引入配置文件，通过flag将配置**文件解析**

  ```golang
  conf := flag.String("conf", defaultConfigPath, "conf file path")
  	flag.Parse()                            // 解析命令行参数
  	c, err := config.UnmarshalConfig(*conf) //解析配置文件
  	if err != nil {
  		panic(err)
  	}
  ```

- 初始化服务（**1. redis、2. mysql、3. 多链实例、4. 日志服务**）

- 启动**路由**服务，**监听**服务端口

- 启用**恢复中间件**，捕获http请求中panic并计入到日志中(不捕获程序会**奔溃**)

- 启用**缓存中间件，**那个路径使用就根据路径、请求参数、请求体组合成KEY,将数据库查询的数据定时缓存在redis中，未过期下次直接取值。

- 启用**日志服务中间件**，记录所有的http请求

- 使用**cors中间件** 允许所有域名访问。配置请求方法、请求头、预请求等

- 路由**分组**定义具体的路由地址，以及执行的**方法**和路由**中间件**

​	

## **业务模块总结**

### **1. 用户认证模块**

#### 业务流程

- 根据用户address生成**UUID**,缓存至redis中72h。登录根据用户地址再次生成UUID判断是否与缓存**一致**；一致则根据用户address查询用户信息；查询到根据用户address生成token**返回**；查询不到则**创建**新用户并返回token。

  

- 根据用户地址查询用户是否已**签名**，是否签名过合约 签名过合约才能进行后续操作 如**创建**NFT 出售NFT**等操作**

#### 数据流向
用户 → API → 验证 → Redis → 响应



### **2. NFT集合模块**

#### 业务流程

- **获取指定NFT集合的详细信息**。根据**链ID**和合约地址（NFT**集合地址**），查询24小时交易信息、查询NFT上架并**缓存**上架数量(链ID和合约地址作为key)、查询NFT集合的**地板价**，如果地板价发生变动则需要价格信息事件(EventType、CollevctionAddr、Prtice)缓存到**<u>redis队列</u>**中、查询指定NFT集合的**最高卖单**价格、获取24小时交易量和销售数量、查询总交易量。

  

- **获取指定集合的所有出价信息.。** 根据合约地址和page以及pageSzie。查询订单表中指定集合的地址、订单类型为出价单、订单状态为活跃、未过期的出价信息。价格分组排序分页参数返回。

  

- **获取指定集合下的所有NFT列表。**根据链ID和合约地址以及筛选条件(**排序**等)，获取NFT **Item列表信息**：Item基本信息、订单信息、图片信息、用户持有数量、最近成交价格、最高出价信息。

  

- **获取NFT集合排行榜**（按交易量、地板价等排序），先从缓存中间件中通过key(路径、查询参数和请求体组合成缓存key),查询是否有缓存直接返回，否则根据limit和时间范围等，将多条链的多个NFT集合数据（**开启对个协程阻塞执行**），按照交易量统一排序返回。





### **3. 单个NFT接口模块**

#### 业务流程

- **获取指定NFT的详细信息（价格、属性、历史等）**。根据合约地址以及**token_id**，**开启多个协程**查询单个NFT 的基本信息、单个NFT的挂单情况、单个NFT的图片及视频信息、查询单个NFT最近成交价格、查询单个NFT最高出价信息、**等待**所有的查询完成组装数据返回。

  

- **获取指定NFT的所有出价信息（Item级别的出价列表）。**根据合约地址以及**token_id**和page pageSize,讲出价列表增加订单类型字段返回。

  

- **获取指定NFT的属性信息（稀有度% = (拥有该特质的NFT数量 / 集合总NFT数量) × 100、特征(Background : Blue, Yellow, Gray 等、Eyes: Laser Eyes, Sad Eyes, Heart Eyes 等)）。**根据合约地址以及**token_id**，查询NFT的特质信息、查询NFT集合的特质统计、计算出NFT的稀有度和特质返回。

  

- **获取指定NFT的图片（支持缓存）**先从**缓存中间件**中通过key(路径、查询参数和请求体组合成缓存key),查询是否有缓存直接返回，否则根据合约地址、token_id、链ID、查询图片信息并缓存到中间件中。

  

- **获取指定NFT的当前持有者信息，**根据合约地址、**token_id**、链ID、链名称，从**<u>链上直接获取NFT所有者信息</u>**，并更新到数据库中。

  

- **刷新指定NFT的元数据（手动更新）** 根据合约地址、token_id、链ID。<u>**从MQ中**</u>

  



### **4.NFT 集合分析接口模块**

#### 业务流程

- **获取NFT集合中各属性的最高价格信息（属性价值分析）**。根据**链ID**、合约地址（NFT**集合地址**、过滤条件（tokenIds）

  

- **获取指定集合的所有出价信息.。** 根据合约地址和page以及pageSzie。查询订单表中指定集合的地址、订单类型为出价单、订单状态为活跃、未过期的出价信息。价格分组排序分页参数返回。

  

- **获取指定集合下的所有NFT列表。**根据链ID和合约地址以及筛选条件(**排序**等)，获取NFT **Item列表信息**：Item基本信息、订单信息、图片信息、用户持有数量、最近成交价格、最高出价信息。

  

- **获取NFT集合排行榜**（按交易量、地板价等排序），先从缓存中间件中通过key(路径、查询参数和请求体组合成缓存key),查询是否有缓存直接返回，否则根据limit和时间范围等，将多条链的多个NFT集合数据（**开启对个协程阻塞执行**），按照交易量统一排序返回。

